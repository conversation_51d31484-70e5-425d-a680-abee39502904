# Cursor 自动注册账号功能实现指南

## 1. 功能概述

Cursor 自动注册账号功能是一个完全自动化的账号注册系统，旨在为用户自动创建 Cursor Pro 账号并完成认证流程。该功能通过模拟浏览器操作、自动填写表单、处理验证码、绕过人机验证等步骤，实现无人工干预的账号注册过程。

### 主要功能特性
- **全自动注册流程**：从账号生成到注册完成的全流程自动化
- **智能验证码处理**：支持邮箱验证码自动获取和输入
- **人机验证绕过**：集成 Turnstile 验证处理机制
- **多平台支持**：支持 Windows、macOS、Linux 系统
- **配置灵活性**：支持多种邮箱服务和自定义配置

## 2. 技术实现细节

### 2.1 核心文件结构

| 文件名 | 功能描述 |
|--------|----------|
| `cursor_pro_keep_alive.py` | 主程序入口，包含注册流程控制逻辑 |
| `cursor_auth_manager.py` | Cursor 认证信息管理器，负责更新本地认证数据 |
| `get_email_code.py` | 邮箱验证码处理模块，支持多种邮箱服务 |
| `browser_utils.py` | 浏览器管理工具，负责浏览器初始化和配置 |
| `config.py` | 配置管理模块，处理环境变量和配置文件 |
| `reset_machine.py` | 机器码重置工具，用于重置设备标识 |
| `language.py` | 多语言支持模块 |

### 2.2 关键类和函数

#### EmailGenerator 类
```python
class EmailGenerator:
    def __init__(self, password="随机生成的12位密码"):
        # 初始化邮箱生成器，加载姓名数据集
    
    def generate_email(self, length=4):
        # 生成随机邮箱地址
    
    def generate_random_name(self):
        # 从姓名数据集中随机选择姓名
```

#### EmailVerificationHandler 类
```python
class EmailVerificationHandler:
    def __init__(self, account):
        # 初始化邮箱验证处理器
    
    def get_verification_code(self, max_retries=5, retry_interval=60):
        # 获取邮箱验证码，支持重试机制
```

#### CursorAuthManager 类
```python
class CursorAuthManager:
    def update_auth(self, email, access_token, refresh_token):
        # 更新 Cursor 本地认证信息到 SQLite 数据库
```

### 2.3 使用的第三方库

- **DrissionPage**: 浏览器自动化操作
- **requests**: HTTP 请求处理
- **sqlite3**: 本地数据库操作
- **imaplib/poplib**: 邮箱协议支持
- **colorama**: 终端彩色输出
- **python-dotenv**: 环境变量管理

## 3. 注册流程步骤

### 3.1 初始化阶段
1. **程序启动**：加载配置文件和环境变量
2. **语言选择**：用户选择界面语言（中文/英文）
3. **版本检查**：检查 Cursor 版本以确定重置策略
4. **模式选择**：用户选择仅重置机器码或完整注册流程

### 3.2 账号生成阶段
1. **随机信息生成**：
   - 从姓名数据集随机选择 first_name 和 last_name
   - 生成12位随机密码（包含大小写字母、数字、特殊字符）
   - 基于姓名和时间戳生成唯一邮箱地址

### 3.3 浏览器初始化阶段
1. **浏览器配置**：
   - 加载 Turnstile 绕过插件
   - 设置用户代理和代理服务器
   - 配置无头模式和其他浏览器参数
2. **邮箱服务初始化**：根据配置选择临时邮箱或 IMAP 服务

### 3.4 注册执行阶段
1. **访问注册页面**：导航到 `https://authenticator.cursor.sh/sign-up`
2. **填写个人信息**：
   - 输入 first_name（随机延迟1-3秒）
   - 输入 last_name（随机延迟1-3秒）
   - 输入邮箱地址（随机延迟1-3秒）
   - 提交个人信息表单
3. **处理人机验证**：调用 `handle_turnstile()` 函数绕过验证
4. **设置密码**：
   - 检测密码输入页面
   - 输入生成的随机密码
   - 提交密码表单
5. **邮箱验证**：
   - 检测验证码输入界面
   - 调用邮箱服务获取6位数字验证码
   - 逐位输入验证码（每位间隔0.1-0.3秒）

### 3.5 完成阶段
1. **获取会话令牌**：从浏览器 Cookie 中提取 `WorkosCursorSessionToken`
2. **更新本地认证**：将邮箱、访问令牌写入 Cursor 配置数据库
3. **重置机器码**：根据版本选择重置策略
4. **显示账号信息**：输出生成的账号和密码信息

## 4. 数据结构

### 4.1 账号信息结构
```python
account_info = {
    "email": "生成的邮箱地址",
    "password": "12位随机密码", 
    "first_name": "随机姓名",
    "last_name": "随机姓氏"
}
```

### 4.2 认证数据结构
```python
auth_data = {
    "cursorAuth/cachedSignUpType": "Auth_0",
    "cursorAuth/cachedEmail": "用户邮箱",
    "cursorAuth/accessToken": "访问令牌",
    "cursorAuth/refreshToken": "刷新令牌"
}
```

### 4.3 机器标识数据结构
```python
machine_ids = {
    "telemetry.devDeviceId": "UUID格式设备ID",
    "telemetry.macMachineId": "128位十六进制机器ID", 
    "telemetry.machineId": "64位十六进制机器ID",
    "telemetry.sqmId": "大写UUID格式ID"
}
```

## 5. 错误处理

### 5.1 网络相关错误
- **连接超时**：自动重试机制，最多重试3次
- **页面加载失败**：记录错误日志并终止流程
- **代理连接问题**：提示用户检查代理配置

### 5.2 验证相关错误
- **人机验证失败**：最多重试2次，失败后保存截图
- **邮箱验证码获取失败**：重试5次，每次间隔60秒
- **验证码输入错误**：记录错误并重新获取

### 5.3 系统相关错误
- **配置文件缺失**：抛出 `FileNotFoundError` 异常
- **权限不足**：提示用户以管理员身份运行
- **数据库访问失败**：检查 Cursor 安装路径和权限

### 5.4 业务逻辑错误
- **邮箱已被使用**：终止注册流程并记录错误
- **密码设置失败**：记录详细错误信息
- **会话令牌获取失败**：提示用户手动检查

## 6. 配置参数

### 6.1 环境变量配置（.env 文件）

```bash
# 临时邮箱配置（二选一）
TEMP_MAIL=用户名部分
TEMP_MAIL_EPIN=邮箱PIN码
TEMP_MAIL_EXT=@域名后缀
DOMAIN=邮箱域名

# IMAP邮箱配置（二选一）
IMAP_SERVER=IMAP服务器地址
IMAP_PORT=IMAP端口号
IMAP_USER=邮箱用户名
IMAP_PASS=邮箱密码
IMAP_DIR=收件箱目录名

# 浏览器配置
BROWSER_PATH=浏览器可执行文件路径
BROWSER_PROXY=代理服务器地址
BROWSER_HEADLESS=是否无头模式(True/False)
```

### 6.2 系统路径配置

| 操作系统 | Cursor 配置路径 |
|----------|----------------|
| Windows | `%APPDATA%\Cursor\User\globalStorage\state.vscdb` |
| macOS | `~/Library/Application Support/Cursor/User/globalStorage/state.vscdb` |
| Linux | `~/.config/Cursor/User/globalStorage/state.vscdb` |

## 7. 流程图

```mermaid
flowchart TD
    A[程序启动] --> B[加载配置文件]
    B --> C[选择语言]
    C --> D[检查Cursor版本]
    D --> E[选择操作模式]
    E --> F{仅重置机器码?}
    F -->|是| G[重置机器码]
    F -->|否| H[初始化浏览器]
    H --> I[生成随机账号信息]
    I --> J[初始化邮箱服务]
    J --> K[访问注册页面]
    K --> L[填写个人信息]
    L --> M[处理Turnstile验证]
    M --> N{验证通过?}
    N -->|否| O[重试验证]
    O --> N
    N -->|是| P[设置密码]
    P --> Q[检查邮箱可用性]
    Q --> R{邮箱可用?}
    R -->|否| S[注册失败]
    R -->|是| T[获取邮箱验证码]
    T --> U{获取成功?}
    U -->|否| V[重试获取验证码]
    V --> U
    U -->|是| W[输入验证码]
    W --> X[等待系统处理]
    X --> Y[获取会话令牌]
    Y --> Z[更新本地认证信息]
    Z --> AA[重置机器码]
    AA --> BB[显示账号信息]
    G --> CC[完成]
    BB --> CC
    S --> CC
```

## 8. 核心代码实现示例

### 8.1 账号注册主函数

<augment_code_snippet path="cursor_pro_keep_alive.py" mode="EXCERPT">
```python
def sign_up_account(browser, tab):
    logging.info(get_translation("start_account_registration"))
    logging.info(get_translation("visiting_registration_page", url=sign_up_url))
    tab.get(sign_up_url)

    try:
        if tab.ele("@name=first_name"):
            logging.info(get_translation("filling_personal_info"))
            tab.actions.click("@name=first_name").input(first_name)
            logging.info(get_translation("input_first_name", name=first_name))
            time.sleep(random.uniform(1, 3))

            tab.actions.click("@name=last_name").input(last_name)
            logging.info(get_translation("input_last_name", name=last_name))
            time.sleep(random.uniform(1, 3))

            tab.actions.click("@name=email").input(account)
            logging.info(get_translation("input_email", email=account))
            time.sleep(random.uniform(1, 3))

            logging.info(get_translation("submitting_personal_info"))
            tab.actions.click("@type=submit")
```
</augment_code_snippet>

### 8.2 Turnstile 验证处理

<augment_code_snippet path="cursor_pro_keep_alive.py" mode="EXCERPT">
```python
def handle_turnstile(tab, max_retries: int = 2, retry_interval: tuple = (1, 2)) -> bool:
    logging.info(get_translation("detecting_turnstile"))
    save_screenshot(tab, "start")

    retry_count = 0

    try:
        while retry_count < max_retries:
            retry_count += 1
            logging.debug(get_translation("retry_verification", count=retry_count))

            try:
                # Locate verification frame element
                challenge_check = (
                    tab.ele("@id=cf-turnstile", timeout=2)
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )

                if challenge_check:
                    logging.info(get_translation("detected_turnstile"))
                    # Random delay before clicking verification
                    time.sleep(random.uniform(1, 3))
                    challenge_check.click()
                    time.sleep(2)
```
</augment_code_snippet>

### 8.3 邮箱验证码获取

<augment_code_snippet path="get_email_code.py" mode="EXCERPT">
```python
def get_verification_code(self, max_retries=5, retry_interval=60):
    for attempt in range(max_retries):
        try:
            logging.info(f"尝试获取验证码 (第 {attempt + 1}/{max_retries} 次)...")

            if not self.imap:
                verify_code, first_id = self._get_latest_mail_code()
                if verify_code is not None and first_id is not None:
                    self._cleanup_mail(first_id)
                    return verify_code
            else:
                if self.protocol.upper() == 'IMAP':
                    verify_code = self._get_mail_code_by_imap()
                else:
                    verify_code = self._get_mail_code_by_pop3()
                if verify_code is not None:
                    return verify_code
```
</augment_code_snippet>

## 9. 安全性考虑

### 9.1 数据安全
- **密码生成**：使用强随机密码生成算法，包含大小写字母、数字和特殊字符
- **本地存储**：认证信息存储在本地 SQLite 数据库中，不上传到远程服务器
- **临时数据清理**：注册完成后自动清理临时邮件和浏览器数据

### 9.2 反检测机制
- **随机延迟**：在各个操作步骤之间添加随机延迟，模拟人工操作
- **用户代理伪装**：动态获取和设置真实的浏览器用户代理
- **行为模拟**：通过 DrissionPage 模拟真实的鼠标点击和键盘输入

### 9.3 隐私保护
- **无头模式**：支持无头浏览器模式，减少界面暴露
- **代理支持**：支持通过代理服务器访问，保护真实 IP 地址
- **日志脱敏**：敏感信息在日志中进行脱敏处理

## 10. 性能优化

### 10.1 并发处理
- **异步操作**：邮箱验证码获取采用异步重试机制
- **超时控制**：所有网络请求都设置合理的超时时间
- **资源管理**：及时释放浏览器和网络连接资源

### 10.2 错误恢复
- **断点续传**：支持从失败点重新开始注册流程
- **状态保存**：关键状态信息保存到本地，支持程序重启后恢复
- **智能重试**：根据错误类型采用不同的重试策略

## 11. 扩展性设计

### 11.1 邮箱服务扩展
- **插件化架构**：支持添加新的邮箱服务提供商
- **协议支持**：同时支持 IMAP、POP3 和 HTTP API 方式
- **配置灵活**：通过配置文件轻松切换不同的邮箱服务

### 11.2 多语言支持
- **国际化框架**：基于 language.py 模块实现多语言支持
- **动态切换**：运行时支持语言切换
- **本地化适配**：支持不同地区的日期、时间格式

## 12. 监控和调试

### 12.1 日志系统
- **分级日志**：支持 DEBUG、INFO、WARNING、ERROR 多个级别
- **彩色输出**：使用 colorama 库实现彩色终端输出
- **文件记录**：重要操作记录到日志文件中

### 12.2 调试工具
- **截图功能**：关键步骤自动保存页面截图
- **状态检查**：实时检查注册流程各个阶段的状态
- **错误追踪**：详细的错误堆栈信息和上下文

## 13. 部署和维护

### 13.1 环境要求
- **Python 版本**：Python 3.7+
- **浏览器要求**：Chrome/Chromium 浏览器
- **系统支持**：Windows 10+、macOS 10.14+、Ubuntu 18.04+

### 13.2 依赖管理
- **requirements.txt**：明确列出所有依赖包及版本
- **虚拟环境**：建议使用虚拟环境隔离依赖
- **版本锁定**：关键依赖包锁定特定版本以确保稳定性

### 13.3 更新维护
- **版本检查**：自动检查 Cursor 版本并适配相应策略
- **配置迁移**：支持配置文件格式的向后兼容
- **功能开关**：通过配置开关控制新功能的启用

---

*本文档详细描述了 Cursor 自动注册账号功能的完整实现机制，包括技术细节、流程步骤、数据结构、错误处理、安全性考虑和配置参数。该功能通过自动化技术实现了从账号生成到注册完成的全流程操作，具有高度的可靠性、安全性和可扩展性。*
