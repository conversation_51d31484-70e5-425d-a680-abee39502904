# 项目工作原理分析

## 1. 项目结构与模块功能

- **cursor_auth_manager.py**  
  负责管理 Cursor 编辑器的认证信息（如邮箱、access_token、refresh_token），通过操作本地 state.vscdb 数据库实现认证信息的读取和更新。支持多操作系统路径适配。

- **cursor_pro_keep_alive.py**  
  可能用于保持 Cursor Pro 账号的活跃状态，防止自动登出。

- **patch_cursor_get_machine_id.py**  
  可能用于修改或伪造机器码，以绕过某些本地限制。

- **reset_machine.py**  
  可能用于重置本地 Cursor 相关的机器信息，恢复初始状态。

- **config.py**  
  集中管理项目的配置信息，如路径、常量、环境变量等。

- **logger.py**  
  统一日志输出，便于调试和追踪。

- **README.md / README.EN.md**  
  提供项目说明和使用文档。

- **test/**  
  存放测试脚本，验证各模块功能。

## 2. 典型工作流程

1. 用户通过脚本（如 get_email_code.py 或 cursor_auth_manager.py）更新认证信息，相关信息写入本地数据库。
2. cursor_pro_keep_alive.py 负责定时刷新 token 或保持活跃，防止账号掉线。
3. patch_cursor_get_machine_id.py、reset_machine.py 可用于环境伪装或重置，辅助账号管理。
4. 日志和配置模块为各脚本提供支持，便于维护和扩展。

## 3. Mermaid 流程图

```mermaid
flowchart TD
    A[启动脚本] --> B{需要认证?}
    B -- 是 --> C[调用 cursor_auth_manager.py 更新认证信息]
    C --> D[写入 state.vscdb 数据库]
    B -- 否 --> E{需要保持活跃?}
    D --> E
    E -- 是 --> F[运行 cursor_pro_keep_alive.py 保持登录]
    F --> G{需要环境伪装/重置?}
    E -- 否 --> G
    G -- 是 --> H[运行 patch_cursor_get_machine_id.py 或 reset_machine.py]
    G -- 否 --> I[正常使用 Cursor 编辑器]
    H --> I
```

## 4. 总结

本项目通过一系列脚本和工具，自动化管理 Cursor 编辑器的认证、登录状态和本地环境，提升账号使用的灵活性和便捷性。各模块分工明确，协同工作，便于扩展和维护。
---

Ran tool

Read file: get_email_code.py

Read file: test_email.py

Read file: config.py

Read file: .env.example

Read file: cursor_pro_keep_alive.py

Ran tool

Ran tool

Ran tool

Read file: README.md

Ran tool

Ran tool

Ran tool

Ran tool
# Cursor 项目邮箱认证流程分析

## 邮箱类型与配置

项目支持两种邮箱验证方式：

1. **临时邮箱 (tempmail.plus)**
   - 配置参数：`TEMP_MAIL`、`TEMP_MAIL_EPIN`、`TEMP_MAIL_EXT`
   - 自动获取验证码，无需用户干预
   - 适合临时使用场景

2. **IMAP/POP3 邮箱服务器**
   - 当 `TEMP_MAIL=null` 时启用
   - 配置参数：`IMAP_SERVER`、`IMAP_PORT`、`IMAP_USER`、`IMAP_PASS`、`IMAP_DIR`
   - 支持 `IMAP` 或 `POP3` 协议（通过 `IMAP_PROTOCOL` 设置）
   - 特别支持网易邮箱（163.com、126.com、yeah.net）等

## 邮箱地址生成

1. **随机生成邮箱**
   - 使用 `EmailGenerator` 类生成随机邮箱
   - 基于 `names-dataset.txt` 文件的名字数据
   - 生成格式：`随机名字 + 随机数字 + @配置域名`

2. **固定邮箱**
   - 通过配置文件直接指定使用的邮箱地址

## 登录认证流程

```mermaid
flowchart TD
    A[启动 cursor_pro_keep_alive.py] --> B[生成随机邮箱或使用配置邮箱]
    B --> C[访问 Cursor 注册/登录页面]
    C --> D[填写注册表单]
    D --> E[处理 Turnstile 验证]
    E --> F[发送验证码到邮箱]
    F --> G[EmailVerificationHandler 获取验证码]
    G --> H[输入验证码完成验证]
    H --> I[获取 WorkosCursorSessionToken]
    I --> J[提取 access_token/refresh_token]
    J --> K[调用 CursorAuthManager.update_auth]
    K --> L[更新本地 state.vscdb 数据库]
```

## 验证码获取流程

1. **临时邮箱方式**
   - 调用 tempmail.plus API 获取邮件列表
   - 获取最新邮件内容
   - 使用正则表达式提取 6 位数字验证码
   - 清理邮件

2. **IMAP/POP3 方式**
   - 连接到邮箱服务器（IMAP 或 POP3）
   - 搜索邮件（根据日期或发件人）
   - 提取邮件内容并解析
   - 使用正则表达式提取 6 位数字验证码

## 本地认证信息存储

CursorAuthManager 根据操作系统选择正确的数据库路径：
- Windows: `%APPDATA%/Cursor/User/globalStorage/state.vscdb`
- macOS: `~/Library/Application Support/Cursor/User/globalStorage/state.vscdb`
- Linux: `~/.config/Cursor/User/globalStorage/state.vscdb`

更新的关键信息：
- `cursorAuth/cachedSignUpType`: 设置为 `Auth_0`（表示已登录）
- `cursorAuth/cachedEmail`: 邮箱地址
- `cursorAuth/accessToken`: 访问令牌
- `cursorAuth/refreshToken`: 刷新令牌

## 保持登录状态

通过 `cursor_pro_keep_alive.py` 定期运行，保持令牌有效，防止登录状态失效。如需要，可通过 `reset_machine.py` 重置机器 ID 以绕过限制。
